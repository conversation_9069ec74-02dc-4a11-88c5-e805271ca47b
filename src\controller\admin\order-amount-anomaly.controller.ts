import {
  Controller,
  Get,
  Post,
  Inject,
  Query,
  Param,
  Body,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { OrderAmountAnomalyService } from '../../service/order-amount-anomaly.service';
import { CustomError } from '../../error/custom.error';
import { AnomalyProcessStatus, OrderAmountAnomalyType } from '../../entity';

@Controller('/admin/order-amount-anomalies')
export class AdminOrderAmountAnomalyController {
  @Inject()
  ctx: Context;

  @Inject()
  orderAmountAnomalyService: OrderAmountAnomalyService;

  @Get('/check', { summary: '检查订单金额异常' })
  async checkAnomalies(
    @Query('threshold') threshold?: number,
    @Query('absoluteThreshold') absoluteThreshold?: number,
    @Query('limit') limit?: number,
    @Query('orderId') orderId?: number,
    @Query('skipExisting') skipExisting?: string,
    @Query('clearExistingRecords') clearExistingRecords?: string
  ) {
    return await this.orderAmountAnomalyService.checkOrderAmountAnomalies({
      threshold: threshold ? parseFloat(threshold.toString()) : undefined,
      absoluteThreshold: absoluteThreshold
        ? parseFloat(absoluteThreshold.toString())
        : undefined,
      limit: limit ? parseInt(limit.toString()) : undefined,
      orderId: orderId ? parseInt(orderId.toString()) : undefined,
      skipExisting: skipExisting !== 'false',
      clearExistingRecords: clearExistingRecords === 'true',
    });
  }

  @Post('/batch-check', { summary: '批量检查并创建异常记录' })
  async batchCheckAndCreate(
    @Body()
    body: {
      threshold?: number;
      absoluteThreshold?: number;
      limit?: number;
      autoCreateRecords?: boolean;
      clearExistingRecords?: boolean;
    } = {}
  ) {
    return await this.orderAmountAnomalyService.batchCheckAndCreateAnomalies({
      threshold: body.threshold,
      absoluteThreshold: body.absoluteThreshold,
      limit: body.limit,
      autoCreateRecords: !body.autoCreateRecords,
      clearExistingRecords: body.clearExistingRecords,
    });
  }

  @Get('/records', { summary: '获取异常记录列表' })
  async getAnomalyRecords(
    @Query('status') status?: string,
    @Query('anomalyType') anomalyType?: string,
    @Query('severity') severity?: string,
    @Query('canAutoFix') canAutoFix?: boolean,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number
  ) {
    const statusArray = status
      ? (status.split(',') as AnomalyProcessStatus[])
      : undefined;
    const typeArray = anomalyType
      ? (anomalyType.split(',') as OrderAmountAnomalyType[])
      : undefined;
    const severityArray = severity
      ? severity
          .split(',')
          .map(s => parseInt(s))
          .filter(s => !isNaN(s))
      : undefined;

    return await this.orderAmountAnomalyService.getAnomalyRecords({
      status: statusArray,
      anomalyType: typeArray,
      severity: severityArray,
      canAutoFix,
      limit: limit ? parseInt(limit.toString()) : undefined,
      offset: offset ? parseInt(offset.toString()) : undefined,
    });
  }

  @Post('/auto-fix/:recordId', { summary: '自动修复异常' })
  async autoFixAnomaly(@Param('recordId') recordId: number) {
    if (!recordId) {
      throw new CustomError('异常记录ID不能为空');
    }
    return await this.orderAmountAnomalyService.autoFixAnomaly(
      parseInt(recordId.toString())
    );
  }

  @Post('/batch-auto-fix', { summary: '批量自动修复异常' })
  async batchAutoFixAnomalies(
    @Query('recordIds') recordIds?: string,
    @Query('maxAttempts') maxAttempts?: number,
    @Query('onlyAutoFixable') onlyAutoFixable?: string
  ) {
    const ids = recordIds
      ? recordIds
          .split(',')
          .map(id => parseInt(id.trim()))
          .filter(id => !isNaN(id))
      : undefined;

    return await this.orderAmountAnomalyService.batchAutoFixAnomalies({
      recordIds: ids,
      maxAttempts: maxAttempts ? parseInt(maxAttempts.toString()) : undefined,
      onlyAutoFixable: onlyAutoFixable !== 'false',
    });
  }

  @Post('/manual-fix/:recordId', { summary: '手动修复异常' })
  async manualFixAnomaly(
    @Param('recordId') recordId: number,
    @Body()
    fixData: {
      originalPrice?: number;
      totalFee?: number;
      cardDeduction?: number;
      couponDeduction?: number;
      operatorId: number;
      operatorName: string;
      remark?: string;
    }
  ) {
    if (!recordId) {
      throw new CustomError('异常记录ID不能为空');
    }

    if (!fixData.operatorId || !fixData.operatorName) {
      throw new CustomError('操作人信息不能为空');
    }

    const { operatorId, operatorName, remark, ...updateData } = fixData;

    return await this.orderAmountAnomalyService.manualFixAnomaly(
      parseInt(recordId.toString()),
      updateData,
      { operatorId, operatorName, remark }
    );
  }

  @Post('/revert/:logId', { summary: '回退修复操作' })
  async revertFix(
    @Param('logId') logId: number,
    @Body()
    revertData: {
      operatorId: number;
      operatorName: string;
      reason: string;
    }
  ) {
    if (!logId) {
      throw new CustomError('修复日志ID不能为空');
    }

    if (
      !revertData.operatorId ||
      !revertData.operatorName ||
      !revertData.reason
    ) {
      throw new CustomError('回退操作信息不完整');
    }

    return await this.orderAmountAnomalyService.revertFix(
      parseInt(logId.toString()),
      revertData
    );
  }

  @Get('/statistics', { summary: '获取异常统计报告' })
  async getAnomalyStatistics() {
    return await this.orderAmountAnomalyService.getAnomalyStatistics();
  }

  @Get('/fix-logs', { summary: '获取修复日志' })
  async getFixLogs(
    @Query('anomalyRecordId') anomalyRecordId?: number,
    @Query('orderId') orderId?: number,
    @Query('operationType') operationType?: string,
    @Query('result') result?: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number
  ) {
    return await this.orderAmountAnomalyService.getFixLogs({
      anomalyRecordId: anomalyRecordId
        ? parseInt(anomalyRecordId.toString())
        : undefined,
      orderId: orderId ? parseInt(orderId.toString()) : undefined,
      operationType: operationType
        ? (operationType.split(',') as any[])
        : undefined,
      result: result ? (result.split(',') as any[]) : undefined,
      limit: limit ? parseInt(limit.toString()) : undefined,
      offset: offset ? parseInt(offset.toString()) : undefined,
    });
  }

  @Get('/report', { summary: '生成异常问题清单' })
  async generateAnomalyReport(
    @Query('status') status?: string,
    @Query('severity') severity?: string,
    @Query('includeDetails') includeDetails?: string
  ) {
    const statusArray = status
      ? (status.split(',') as AnomalyProcessStatus[])
      : undefined;
    const severityArray = severity
      ? severity
          .split(',')
          .map(s => parseInt(s))
          .filter(s => !isNaN(s))
      : undefined;

    return await this.orderAmountAnomalyService.generateAnomalyReport({
      status: statusArray,
      severity: severityArray,
      includeDetails: includeDetails !== 'false',
    });
  }

  @Post('/ignore/:recordId', { summary: '忽略异常记录' })
  async ignoreAnomaly(
    @Param('recordId') recordId: number,
    @Body()
    ignoreData: {
      operatorId: number;
      operatorName: string;
      reason: string;
    }
  ) {
    if (!recordId) {
      throw new CustomError('异常记录ID不能为空');
    }

    if (
      !ignoreData.operatorId ||
      !ignoreData.operatorName ||
      !ignoreData.reason
    ) {
      throw new CustomError('忽略操作信息不完整');
    }

    return await this.orderAmountAnomalyService.ignoreAnomaly(
      parseInt(recordId.toString()),
      ignoreData
    );
  }

  @Post('/cleanup-invalid', {
    summary: '清理无效的异常记录（已取消、待付款订单）',
  })
  async cleanupInvalidRecords() {
    return await this.orderAmountAnomalyService.cleanupInvalidAnomalyRecords();
  }

  @Post('/update-fix-suggestions', { summary: '更新现有异常记录的修复建议' })
  async updateFixSuggestions() {
    return await this.orderAmountAnomalyService.updateFixSuggestions();
  }
}
