/**
 * 订单金额不一致修复脚本
 * 用于修复 totalFee ≠ originalPrice - cardDeduction - couponDeduction 的问题
 */

import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { QueryTypes } from 'sequelize';
import { Order, OrderDetail, Service } from '../entity';

@Provide()
export class OrderAmountFixService {
  @Inject()
  ctx: Context;

  /**
   * 检查并修复订单金额不一致问题
   */
  async fixOrderAmountInconsistency(options: {
    dryRun?: boolean; // 是否只检查不修复
    orderId?: number; // 指定订单ID
    autoFix?: boolean; // 是否自动修复
  } = {}) {
    const { dryRun = true, orderId, autoFix = false } = options;
    
    console.log('=== 订单金额不一致修复脚本 ===');
    console.log(`模式: ${dryRun ? '检查模式' : '修复模式'}`);
    console.log(`自动修复: ${autoFix ? '是' : '否'}`);
    
    try {
      // 1. 查找不一致的主订单
      const inconsistentOrders = await this.findInconsistentOrders(orderId);
      
      console.log(`\n发现 ${inconsistentOrders.length} 个不一致的主订单:`);
      
      const fixResults = [];
      
      for (const orderData of inconsistentOrders) {
        console.log(`\n处理订单 ${orderData.sn}:`);
        console.log(`  当前原价: ${orderData.originalPrice}`);
        console.log(`  权益卡抵扣: ${orderData.cardDeduction}`);
        console.log(`  代金券抵扣: ${orderData.couponDeduction}`);
        console.log(`  当前实付: ${orderData.totalFee}`);
        console.log(`  计算实付: ${orderData.calculatedTotalFee}`);
        console.log(`  差异: ${orderData.difference}`);
        
        // 分析问题类型
        const analysis = await this.analyzeOrderProblem(orderData);
        console.log(`  问题分析: ${analysis.description}`);
        console.log(`  建议修复: ${analysis.suggestion}`);
        
        if (!dryRun && (autoFix || analysis.canAutoFix)) {
          const fixResult = await this.fixOrder(orderData, analysis);
          fixResults.push(fixResult);
          console.log(`  修复结果: ${fixResult.success ? '成功' : '失败'}`);
          if (!fixResult.success) {
            console.log(`  失败原因: ${fixResult.error}`);
          }
        }
      }
      
      // 2. 检查追加服务订单
      const inconsistentAdditionalOrders = await this.findInconsistentAdditionalOrders();
      console.log(`\n发现 ${inconsistentAdditionalOrders.length} 个不一致的追加服务订单`);
      
      // 3. 总结
      console.log('\n=== 修复总结 ===');
      console.log(`主订单不一致数量: ${inconsistentOrders.length}`);
      console.log(`追加服务不一致数量: ${inconsistentAdditionalOrders.length}`);
      
      if (!dryRun) {
        const successCount = fixResults.filter(r => r.success).length;
        console.log(`修复成功: ${successCount}`);
        console.log(`修复失败: ${fixResults.length - successCount}`);
      }
      
      return {
        inconsistentMainOrders: inconsistentOrders.length,
        inconsistentAdditionalOrders: inconsistentAdditionalOrders.length,
        fixResults: dryRun ? [] : fixResults,
      };
      
    } catch (error) {
      console.error('修复过程中发生错误:', error);
      throw error;
    }
  }

  /**
   * 查找不一致的主订单
   */
  private async findInconsistentOrders(orderId?: number) {
    const whereClause = orderId ? `AND o.id = ${orderId}` : '';
    
    const query = `
      SELECT 
        o.id,
        o.sn,
        o.status,
        o.originalPrice,
        o.cardDeduction,
        o.couponDeduction,
        o.totalFee,
        (o.originalPrice - o.cardDeduction - o.couponDeduction) as calculatedTotalFee,
        ABS(o.totalFee - (o.originalPrice - o.cardDeduction - o.couponDeduction)) as difference
      FROM orders o
      WHERE o.status IN ('已完成', '已评价', '退款中', '已退款')
        AND ABS(o.totalFee - (o.originalPrice - o.cardDeduction - o.couponDeduction)) > 0.01
        ${whereClause}
      ORDER BY difference DESC
    `;

    return await Order.sequelize.query(query, {
      type: QueryTypes.SELECT,
    }) as any[];
  }

  /**
   * 查找不一致的追加服务订单
   */
  private async findInconsistentAdditionalOrders() {
    const query = `
      SELECT 
        aso.id,
        aso.sn,
        aso.status,
        aso.originalPrice,
        aso.cardDeduction,
        aso.couponDeduction,
        aso.totalFee,
        (aso.originalPrice - aso.cardDeduction - aso.couponDeduction) as calculatedTotalFee,
        ABS(aso.totalFee - (aso.originalPrice - aso.cardDeduction - aso.couponDeduction)) as difference
      FROM additional_service_orders aso
      WHERE aso.status IN ('completed', 'refunding', 'refunded')
        AND ABS(aso.totalFee - (aso.originalPrice - aso.cardDeduction - aso.couponDeduction)) > 0.01
      ORDER BY difference DESC
    `;

    return await Order.sequelize.query(query, {
      type: QueryTypes.SELECT,
    }) as any[];
  }

  /**
   * 分析订单问题
   */
  private async analyzeOrderProblem(orderData: any) {
    // 获取订单详情
    const order = await Order.findByPk(orderData.id, {
      include: [
        {
          model: OrderDetail,
          include: [
            {
              model: Service,
              attributes: ['id', 'serviceName', 'basePrice'],
              required: false,
            },
          ],
        },
      ],
    });

    if (!order) {
      return {
        description: '订单不存在',
        suggestion: '无法修复',
        canAutoFix: false,
      };
    }

    // 计算订单明细总价
    const detailsTotalPrice = order.orderDetails?.reduce((sum, detail) => {
      return sum + (detail.servicePrice || 0);
    }, 0) || 0;

    const problems = [];
    let canAutoFix = false;
    let suggestion = '';

    // 1. 检查原价是否缺失
    if (!orderData.originalPrice || orderData.originalPrice <= 0) {
      problems.push('原价缺失或为0');
      if (detailsTotalPrice > 0) {
        suggestion = `建议设置原价为明细总价: ${detailsTotalPrice}`;
        canAutoFix = true;
      }
    }

    // 2. 检查原价与明细是否匹配
    else if (Math.abs(orderData.originalPrice - detailsTotalPrice) > 0.01) {
      problems.push(`原价(${orderData.originalPrice})与明细总价(${detailsTotalPrice})不匹配`);
      suggestion = `建议修正原价为: ${detailsTotalPrice}`;
      canAutoFix = true;
    }

    // 3. 检查优惠是否超过原价
    const totalDiscount = (orderData.cardDeduction || 0) + (orderData.couponDeduction || 0);
    if (totalDiscount > orderData.originalPrice) {
      problems.push(`优惠金额(${totalDiscount})超过原价(${orderData.originalPrice})`);
      suggestion = '需要人工检查优惠设置';
      canAutoFix = false;
    }

    // 4. 检查实付金额计算
    else if (Math.abs(orderData.totalFee - orderData.calculatedTotalFee) > 0.01) {
      problems.push('实付金额计算错误');
      suggestion = `建议修正实付金额为: ${orderData.calculatedTotalFee}`;
      canAutoFix = true;
    }

    return {
      description: problems.join('; '),
      suggestion,
      canAutoFix,
      detailsTotalPrice,
      recommendedOriginalPrice: detailsTotalPrice,
      recommendedTotalFee: Math.max(0, (detailsTotalPrice || orderData.originalPrice) - totalDiscount),
    };
  }

  /**
   * 修复订单
   */
  private async fixOrder(orderData: any, analysis: any) {
    try {
      const updateData: any = {};

      // 修复原价
      if (analysis.recommendedOriginalPrice && 
          Math.abs(orderData.originalPrice - analysis.recommendedOriginalPrice) > 0.01) {
        updateData.originalPrice = analysis.recommendedOriginalPrice;
      }

      // 修复实付金额
      if (analysis.recommendedTotalFee !== undefined &&
          Math.abs(orderData.totalFee - analysis.recommendedTotalFee) > 0.01) {
        updateData.totalFee = analysis.recommendedTotalFee;
      }

      if (Object.keys(updateData).length > 0) {
        await Order.update(updateData, {
          where: { id: orderData.id },
        });

        return {
          success: true,
          orderId: orderData.id,
          orderSn: orderData.sn,
          updates: updateData,
        };
      } else {
        return {
          success: true,
          orderId: orderData.id,
          orderSn: orderData.sn,
          updates: {},
          message: '无需修复',
        };
      }
    } catch (error) {
      return {
        success: false,
        orderId: orderData.id,
        orderSn: orderData.sn,
        error: error.message,
      };
    }
  }
}
