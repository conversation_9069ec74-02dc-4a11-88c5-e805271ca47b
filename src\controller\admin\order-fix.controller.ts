import { Controller, Post, Query, Body, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { OrderAmountFixService } from '../../scripts/fix-order-amount-inconsistency';

/**
 * 订单修复控制器（管理端）
 */
@Controller('/admin/order-fix')
export class AdminOrderFixController {
  @Inject()
  ctx: Context;

  @Inject()
  orderAmountFixService: OrderAmountFixService;

  /**
   * 检查订单金额不一致问题
   * @summary 检查订单金额不一致问题
   */
  @Post('/check-amount-inconsistency')
  async checkAmountInconsistency(@Query('orderId') orderId?: number) {
    const result = await this.orderAmountFixService.fixOrderAmountInconsistency(
      {
        dryRun: true, // 只检查不修复
        orderId,
        autoFix: false,
      }
    );

    return result;
  }

  /**
   * 修复订单金额不一致问题
   * @summary 修复订单金额不一致问题
   */
  @Post('/fix-amount-inconsistency')
  async fixAmountInconsistency(
    @Body()
    body: {
      orderId?: number;
      autoFix?: boolean;
      confirmFix?: boolean; // 确认修复标志
    }
  ) {
    const { orderId, autoFix = false, confirmFix = false } = body;

    if (!confirmFix) {
      return {
        error: '请确认修复操作，设置 confirmFix: true',
        message: '此操作将修改数据库中的订单金额数据，请谨慎操作',
      };
    }

    const result = await this.orderAmountFixService.fixOrderAmountInconsistency(
      {
        dryRun: false, // 执行修复
        orderId,
        autoFix,
      }
    );

    return result;
  }
}
