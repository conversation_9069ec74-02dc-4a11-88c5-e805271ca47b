/**
 * 收入统计数据一致性测试脚本
 * 用于验证总原价-优惠金额=实收金额的修复效果
 */

const { Sequelize, QueryTypes } = require('sequelize');

// 数据库连接配置（请根据实际情况修改）
const sequelize = new Sequelize({
  dialect: 'mysql',
  host: 'localhost',
  port: 3306,
  username: 'root',
  password: 'password',
  database: 'pet_manager',
  logging: false, // 关闭SQL日志以便查看结果
});

async function testRevenueStatistics() {
  try {
    console.log('=== 收入统计数据一致性测试 ===\n');

    // 1. 测试主订单数据一致性
    console.log('1. 主订单数据一致性检查：');
    const mainOrderQuery = `
      SELECT
        COUNT(*) as totalCount,
        COUNT(CASE WHEN ABS(o.totalFee - (o.originalPrice - o.cardDeduction - o.couponDeduction)) > 0.01 THEN 1 END) as inconsistentCount,
        SUM(o.originalPrice) as sumOriginalPrice,
        SUM(o.cardDeduction + o.couponDeduction) as sumDiscounts,
        SUM(o.totalFee) as sumTotalFee,
        SUM(o.originalPrice - o.cardDeduction - o.couponDeduction) as calculatedTotalFee
      FROM orders o
      WHERE o.status IN ('已完成', '已评价', '退款中', '已退款')
    `;

    const mainResult = await sequelize.query(mainOrderQuery, {
      type: QueryTypes.SELECT,
    });

    console.log('  总订单数:', mainResult[0].totalCount);
    console.log('  数据不一致订单数:', mainResult[0].inconsistentCount);
    console.log('  原价总和:', mainResult[0].sumOriginalPrice);
    console.log('  优惠总和:', mainResult[0].sumDiscounts);
    console.log('  实付总和:', mainResult[0].sumTotalFee);
    console.log('  计算实付总和:', mainResult[0].calculatedTotalFee);
    console.log('  差异:', parseFloat(mainResult[0].sumTotalFee) - parseFloat(mainResult[0].calculatedTotalFee));

    // 2. 测试追加服务数据一致性
    console.log('\n2. 追加服务数据一致性检查：');
    const additionalQuery = `
      SELECT
        COUNT(*) as totalCount,
        COUNT(CASE WHEN ABS(aso.totalFee - (aso.originalPrice - aso.cardDeduction - aso.couponDeduction)) > 0.01 THEN 1 END) as inconsistentCount,
        SUM(aso.originalPrice) as sumOriginalPrice,
        SUM(aso.cardDeduction + aso.couponDeduction) as sumDiscounts,
        SUM(aso.totalFee) as sumTotalFee,
        SUM(aso.originalPrice - aso.cardDeduction - aso.couponDeduction) as calculatedTotalFee
      FROM additional_service_orders aso
      WHERE aso.status IN ('completed', 'refunding', 'refunded')
    `;

    const additionalResult = await sequelize.query(additionalQuery, {
      type: QueryTypes.SELECT,
    });

    console.log('  总订单数:', additionalResult[0].totalCount);
    console.log('  数据不一致订单数:', additionalResult[0].inconsistentCount);
    console.log('  原价总和:', additionalResult[0].sumOriginalPrice);
    console.log('  优惠总和:', additionalResult[0].sumDiscounts);
    console.log('  实付总和:', additionalResult[0].sumTotalFee);
    console.log('  计算实付总和:', additionalResult[0].calculatedTotalFee);
    console.log('  差异:', parseFloat(additionalResult[0].sumTotalFee) - parseFloat(additionalResult[0].calculatedTotalFee));

    // 3. 测试总体数据一致性
    console.log('\n3. 总体数据一致性检查：');
    const mainOriginalPrice = parseFloat(mainResult[0].sumOriginalPrice || '0');
    const additionalOriginalPrice = parseFloat(additionalResult[0].sumOriginalPrice || '0');
    const totalOriginalPrice = mainOriginalPrice + additionalOriginalPrice;

    const mainDiscount = parseFloat(mainResult[0].sumDiscounts || '0');
    const additionalDiscount = parseFloat(additionalResult[0].sumDiscounts || '0');
    const totalDiscount = mainDiscount + additionalDiscount;

    const mainPaidAmount = parseFloat(mainResult[0].sumTotalFee || '0');
    const additionalPaidAmount = parseFloat(additionalResult[0].sumTotalFee || '0');
    const totalPaidAmount = mainPaidAmount + additionalPaidAmount;

    const calculatedPaidAmount = totalOriginalPrice - totalDiscount;
    const difference = Math.abs(totalPaidAmount - calculatedPaidAmount);

    console.log('  总原价:', totalOriginalPrice);
    console.log('  总优惠:', totalDiscount);
    console.log('  计算实收金额 (总原价-优惠):', calculatedPaidAmount);
    console.log('  统计实收金额:', totalPaidAmount);
    console.log('  差异:', difference);

    if (difference > 0.01) {
      console.log('  ❌ 数据不一致：总原价-优惠金额 ≠ 实收金额');
    } else {
      console.log('  ✅ 数据一致性验证通过');
    }

    // 4. 查找具体的不一致订单
    console.log('\n4. 查找不一致的主订单：');
    const inconsistentMainOrders = await sequelize.query(`
      SELECT 
        id, sn, status, originalPrice, totalFee, cardDeduction, couponDeduction,
        (originalPrice - cardDeduction - couponDeduction) as calculatedTotalFee,
        ABS(totalFee - (originalPrice - cardDeduction - couponDeduction)) as difference
      FROM orders o
      WHERE o.status IN ('已完成', '已评价', '退款中', '已退款')
        AND ABS(o.totalFee - (o.originalPrice - o.cardDeduction - o.couponDeduction)) > 0.01
      LIMIT 5
    `, { type: QueryTypes.SELECT });

    if (inconsistentMainOrders.length > 0) {
      console.log('  发现不一致的主订单：');
      inconsistentMainOrders.forEach(order => {
        console.log(`    订单${order.sn}: 原价${order.originalPrice}, 优惠${order.cardDeduction + order.couponDeduction}, 实付${order.totalFee}, 计算实付${order.calculatedTotalFee}, 差异${order.difference}`);
      });
    } else {
      console.log('  ✅ 未发现不一致的主订单');
    }

    console.log('\n5. 查找不一致的追加服务订单：');
    const inconsistentAdditionalOrders = await sequelize.query(`
      SELECT 
        id, sn, status, originalPrice, totalFee, cardDeduction, couponDeduction,
        (originalPrice - cardDeduction - couponDeduction) as calculatedTotalFee,
        ABS(totalFee - (originalPrice - cardDeduction - couponDeduction)) as difference
      FROM additional_service_orders aso
      WHERE aso.status IN ('completed', 'refunding', 'refunded')
        AND ABS(aso.totalFee - (aso.originalPrice - aso.cardDeduction - aso.couponDeduction)) > 0.01
      LIMIT 5
    `, { type: QueryTypes.SELECT });

    if (inconsistentAdditionalOrders.length > 0) {
      console.log('  发现不一致的追加服务订单：');
      inconsistentAdditionalOrders.forEach(order => {
        console.log(`    订单${order.sn}: 原价${order.originalPrice}, 优惠${order.cardDeduction + order.couponDeduction}, 实付${order.totalFee}, 计算实付${order.calculatedTotalFee}, 差异${order.difference}`);
      });
    } else {
      console.log('  ✅ 未发现不一致的追加服务订单');
    }

  } catch (error) {
    console.error('测试过程中发生错误:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行测试
testRevenueStatistics();
