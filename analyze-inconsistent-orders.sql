-- 收入统计数据不一致分析SQL脚本

-- 1. 查找主订单中数据不一致的订单
SELECT 
    '主订单数据不一致' as 类型,
    id as 订单ID,
    sn as 订单编号,
    status as 状态,
    originalPrice as 原价,
    cardDeduction as 权益卡抵扣,
    couponDeduction as 代金券抵扣,
    totalFee as 实付金额,
    (originalPrice - cardDeduction - couponDeduction) as 计算实付金额,
    ABS(totalFee - (originalPrice - cardDeduction - couponDeduction)) as 差异金额,
    CASE 
        WHEN originalPrice IS NULL OR originalPrice = 0 THEN '原价缺失'
        WHEN totalFee != (originalPrice - cardDeduction - couponDeduction) THEN '实付金额计算错误'
        ELSE '其他'
    END as 问题类型
FROM orders o
WHERE o.status IN ('已完成', '已评价', '退款中', '已退款')
    AND ABS(o.totalFee - (o.originalPrice - o.cardDeduction - o.couponDeduction)) > 0.01
ORDER BY 差异金额 DESC
LIMIT 10;

-- 2. 查找追加服务订单中数据不一致的订单
SELECT 
    '追加服务数据不一致' as 类型,
    id as 订单ID,
    sn as 订单编号,
    status as 状态,
    originalPrice as 原价,
    cardDeduction as 权益卡抵扣,
    couponDeduction as 代金券抵扣,
    totalFee as 实付金额,
    (originalPrice - cardDeduction - couponDeduction) as 计算实付金额,
    ABS(totalFee - (originalPrice - cardDeduction - couponDeduction)) as 差异金额,
    CASE 
        WHEN originalPrice IS NULL OR originalPrice = 0 THEN '原价缺失'
        WHEN totalFee != (originalPrice - cardDeduction - couponDeduction) THEN '实付金额计算错误'
        ELSE '其他'
    END as 问题类型
FROM additional_service_orders aso
WHERE aso.status IN ('completed', 'refunding', 'refunded')
    AND ABS(aso.totalFee - (aso.originalPrice - aso.cardDeduction - aso.couponDeduction)) > 0.01
ORDER BY 差异金额 DESC
LIMIT 10;

-- 3. 统计主订单数据一致性概况
SELECT 
    '主订单统计' as 类型,
    COUNT(*) as 总订单数,
    COUNT(CASE WHEN ABS(o.totalFee - (o.originalPrice - o.cardDeduction - o.couponDeduction)) > 0.01 THEN 1 END) as 不一致订单数,
    ROUND(COUNT(CASE WHEN ABS(o.totalFee - (o.originalPrice - o.cardDeduction - o.couponDeduction)) > 0.01 THEN 1 END) * 100.0 / COUNT(*), 2) as 不一致比例,
    SUM(o.originalPrice) as 原价总和,
    SUM(o.cardDeduction + o.couponDeduction) as 优惠总和,
    SUM(o.totalFee) as 实付总和,
    SUM(o.originalPrice - o.cardDeduction - o.couponDeduction) as 计算实付总和,
    SUM(o.totalFee) - SUM(o.originalPrice - o.cardDeduction - o.couponDeduction) as 差异
FROM orders o
WHERE o.status IN ('已完成', '已评价', '退款中', '已退款');

-- 4. 统计追加服务数据一致性概况
SELECT 
    '追加服务统计' as 类型,
    COUNT(*) as 总订单数,
    COUNT(CASE WHEN ABS(aso.totalFee - (aso.originalPrice - aso.cardDeduction - aso.couponDeduction)) > 0.01 THEN 1 END) as 不一致订单数,
    ROUND(COUNT(CASE WHEN ABS(aso.totalFee - (aso.originalPrice - aso.cardDeduction - aso.couponDeduction)) > 0.01 THEN 1 END) * 100.0 / COUNT(*), 2) as 不一致比例,
    SUM(aso.originalPrice) as 原价总和,
    SUM(aso.cardDeduction + aso.couponDeduction) as 优惠总和,
    SUM(aso.totalFee) as 实付总和,
    SUM(aso.originalPrice - aso.cardDeduction - aso.couponDeduction) as 计算实付总和,
    SUM(aso.totalFee) - SUM(aso.originalPrice - aso.cardDeduction - aso.couponDeduction) as 差异
FROM additional_service_orders aso
WHERE aso.status IN ('completed', 'refunding', 'refunded');

-- 5. 分析原价缺失的订单
SELECT 
    '原价缺失订单' as 类型,
    COUNT(*) as 数量,
    SUM(totalFee) as 实付金额总和
FROM orders o
WHERE o.status IN ('已完成', '已评价', '退款中', '已退款')
    AND (o.originalPrice IS NULL OR o.originalPrice = 0);

-- 6. 分析优惠金额异常的订单（优惠大于原价）
SELECT 
    '优惠异常订单' as 类型,
    id as 订单ID,
    sn as 订单编号,
    originalPrice as 原价,
    cardDeduction as 权益卡抵扣,
    couponDeduction as 代金券抵扣,
    (cardDeduction + couponDeduction) as 总优惠,
    totalFee as 实付金额,
    CASE 
        WHEN (cardDeduction + couponDeduction) > originalPrice THEN '优惠超过原价'
        WHEN totalFee < 0 THEN '实付金额为负'
        ELSE '其他异常'
    END as 异常类型
FROM orders o
WHERE o.status IN ('已完成', '已评价', '退款中', '已退款')
    AND (
        (o.cardDeduction + o.couponDeduction) > o.originalPrice 
        OR o.totalFee < 0
    )
ORDER BY (cardDeduction + couponDeduction) DESC
LIMIT 10;

-- 7. 查看订单明细与订单原价的关系
SELECT 
    '订单明细分析' as 类型,
    o.id as 订单ID,
    o.sn as 订单编号,
    o.originalPrice as 订单原价,
    SUM(od.servicePrice) as 明细价格总和,
    ABS(o.originalPrice - SUM(od.servicePrice)) as 差异,
    COUNT(od.id) as 明细数量
FROM orders o
LEFT JOIN order_details od ON o.id = od.orderId
WHERE o.status IN ('已完成', '已评价', '退款中', '已退款')
    AND o.originalPrice IS NOT NULL 
    AND o.originalPrice > 0
GROUP BY o.id, o.sn, o.originalPrice
HAVING ABS(o.originalPrice - SUM(od.servicePrice)) > 0.01
ORDER BY 差异 DESC
LIMIT 10;

-- 8. 总体收入统计验证
SELECT 
    '总体统计验证' as 验证项目,
    (SELECT SUM(originalPrice) FROM orders WHERE status IN ('已完成', '已评价', '退款中', '已退款')) +
    (SELECT SUM(originalPrice) FROM additional_service_orders WHERE status IN ('completed', 'refunding', 'refunded')) as 总原价,
    
    (SELECT SUM(cardDeduction + couponDeduction) FROM orders WHERE status IN ('已完成', '已评价', '退款中', '已退款')) +
    (SELECT SUM(cardDeduction + couponDeduction) FROM additional_service_orders WHERE status IN ('completed', 'refunding', 'refunded')) as 总优惠,
    
    (SELECT SUM(totalFee) FROM orders WHERE status IN ('已完成', '已评价', '退款中', '已退款')) +
    (SELECT SUM(totalFee) FROM additional_service_orders WHERE status IN ('completed', 'refunding', 'refunded')) as 总实付,
    
    ((SELECT SUM(originalPrice) FROM orders WHERE status IN ('已完成', '已评价', '退款中', '已退款')) +
     (SELECT SUM(originalPrice) FROM additional_service_orders WHERE status IN ('completed', 'refunding', 'refunded'))) -
    ((SELECT SUM(cardDeduction + couponDeduction) FROM orders WHERE status IN ('已完成', '已评价', '退款中', '已退款')) +
     (SELECT SUM(cardDeduction + couponDeduction) FROM additional_service_orders WHERE status IN ('completed', 'refunding', 'refunded'))) as 计算实付,
     
    ((SELECT SUM(totalFee) FROM orders WHERE status IN ('已完成', '已评价', '退款中', '已退款')) +
     (SELECT SUM(totalFee) FROM additional_service_orders WHERE status IN ('completed', 'refunding', 'refunded'))) -
    (((SELECT SUM(originalPrice) FROM orders WHERE status IN ('已完成', '已评价', '退款中', '已退款')) +
      (SELECT SUM(originalPrice) FROM additional_service_orders WHERE status IN ('completed', 'refunding', 'refunded'))) -
     ((SELECT SUM(cardDeduction + couponDeduction) FROM orders WHERE status IN ('已完成', '已评价', '退款中', '已退款')) +
      (SELECT SUM(cardDeduction + couponDeduction) FROM additional_service_orders WHERE status IN ('completed', 'refunding', 'refunded')))) as 差异;
